// 这是MessageAcknowledgmentService的剩余部分，需要合并到主文件中

    /// <summary>
    /// 获取待确认的消息
    /// </summary>
    public async Task<PendingMessage?> GetPendingMessageAsync(string clientId, ushort messageId)
    {
        if (string.IsNullOrEmpty(clientId))
            return null;

        try
        {
            if (_pendingMessages.TryGetValue(clientId, out var clientMessages) &&
                clientMessages.TryGetValue(messageId, out var pendingMessage))
            {
                return pendingMessage;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending message {MessageId} for client {ClientId}", messageId, clientId);
            return null;
        }
    }

    /// <summary>
    /// 获取客户端的所有待确认消息
    /// </summary>
    public async Task<IList<PendingMessage>> GetClientPendingMessagesAsync(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return new List<PendingMessage>();

        try
        {
            if (_pendingMessages.TryGetValue(clientId, out var clientMessages))
            {
                return clientMessages.Values.ToList();
            }

            return new List<PendingMessage>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending messages for client {ClientId}", clientId);
            return new List<PendingMessage>();
        }
    }

    /// <summary>
    /// 获取超时的待确认消息
    /// </summary>
    public async Task<IList<PendingMessage>> GetTimedOutMessagesAsync(TimeSpan timeoutThreshold)
    {
        var timedOutMessages = new List<PendingMessage>();

        try
        {
            foreach (var clientMessages in _pendingMessages.Values)
            {
                foreach (var pendingMessage in clientMessages.Values)
                {
                    if (pendingMessage.IsTimedOut(timeoutThreshold))
                    {
                        timedOutMessages.Add(pendingMessage);
                    }
                }
            }

            _logger.LogTrace("Found {Count} timed out messages with threshold {Threshold}ms", 
                timedOutMessages.Count, timeoutThreshold.TotalMilliseconds);

            return timedOutMessages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting timed out messages");
            return new List<PendingMessage>();
        }
    }

    /// <summary>
    /// 移除待确认消息
    /// </summary>
    public async Task<bool> RemovePendingMessageAsync(string clientId, ushort messageId)
    {
        if (string.IsNullOrEmpty(clientId))
            return false;

        try
        {
            if (_pendingMessages.TryGetValue(clientId, out var clientMessages))
            {
                var removed = clientMessages.TryRemove(messageId, out var pendingMessage);
                
                if (removed)
                {
                    _logger.LogTrace("Removed pending message {MessageId} for client {ClientId}", messageId, clientId);
                }

                return removed;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing pending message {MessageId} for client {ClientId}", messageId, clientId);
            return false;
        }
    }

    /// <summary>
    /// 清理客户端的所有待确认消息
    /// </summary>
    public async Task<int> ClearClientPendingMessagesAsync(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return 0;

        try
        {
            if (_pendingMessages.TryRemove(clientId, out var clientMessages))
            {
                var count = clientMessages.Count;
                _logger.LogInformation("Cleared {Count} pending messages for client {ClientId}", count, clientId);
                return count;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing pending messages for client {ClientId}", clientId);
            return 0;
        }
    }

    /// <summary>
    /// 获取确认统计信息
    /// </summary>
    public async Task<AcknowledgmentStatistics> GetStatisticsAsync()
    {
        try
        {
            var statistics = new AcknowledgmentStatistics
            {
                TotalAcknowledgedMessages = Interlocked.Read(ref _totalAcknowledgedMessages),
                TotalAcknowledgedQoS1Messages = Interlocked.Read(ref _totalAcknowledgedQoS1Messages),
                TotalAcknowledgedQoS2Messages = Interlocked.Read(ref _totalAcknowledgedQoS2Messages),
                ClientsWithPendingMessages = _pendingMessages.Count
            };

            var allPendingMessages = new List<PendingMessage>();
            foreach (var clientMessages in _pendingMessages.Values)
            {
                allPendingMessages.AddRange(clientMessages.Values);
            }

            statistics.TotalPendingMessages = allPendingMessages.Count;
            statistics.PendingQoS1Messages = allPendingMessages.Count(m => m.QoSLevel == MqttQoSLevel.AtLeastOnce);
            statistics.PendingQoS2Messages = allPendingMessages.Count(m => m.QoSLevel == MqttQoSLevel.ExactlyOnce);
            statistics.QoS2WaitingForPubRec = allPendingMessages.Count(m => m.QoS2State == QoS2MessageState.WaitingForPubRec);
            statistics.QoS2WaitingForPubComp = allPendingMessages.Count(m => m.QoS2State == QoS2MessageState.WaitingForPubComp);

            if (allPendingMessages.Any())
            {
                statistics.AverageMessageAge = allPendingMessages.Average(m => m.Age.TotalMilliseconds);
                statistics.OldestMessageAge = allPendingMessages.Max(m => m.Age.TotalMilliseconds);
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting acknowledgment statistics");
            return new AcknowledgmentStatistics();
        }
    }

    /// <summary>
    /// 清理过期消息
    /// </summary>
    private async void CleanupExpiredMessages(object? state)
    {
        try
        {
            var timeout = TimeSpan.FromMilliseconds(_options.AcknowledgmentTimeoutMs);
            var expiredMessages = await GetTimedOutMessagesAsync(timeout);

            var cleanedCount = 0;
            foreach (var expiredMessage in expiredMessages)
            {
                if (await RemovePendingMessageAsync(expiredMessage.ClientId, expiredMessage.MessageId))
                {
                    cleanedCount++;
                    _logger.LogWarning("Removed expired pending message {MessageId} for client {ClientId}, Age: {Age}ms",
                        expiredMessage.MessageId, expiredMessage.ClientId, expiredMessage.Age.TotalMilliseconds);
                }
            }

            if (cleanedCount > 0)
            {
                _logger.LogInformation("Cleaned up {Count} expired pending messages", cleanedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during expired message cleanup");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _cleanupTimer?.Dispose();
            _pendingMessages.Clear();
            
            _disposed = true;
            _logger.LogInformation("Message Acknowledgment Service disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing Message Acknowledgment Service");
        }
    }
}
